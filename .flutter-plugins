# This is a generated file; do not edit or check into version control.
audio_session=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/
device_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/
flutter_background_service=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service-5.1.0/
flutter_background_service_android=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_android-6.3.0/
flutter_background_service_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_ios-5.0.3/
flutter_local_notifications=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/
flutter_local_notifications_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
flutter_secure_storage_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
just_audio=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.44/
just_audio_web=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.13/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
screen_brightness=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness-0.2.2+1/
screen_brightness_android=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_android-0.1.0+2/
screen_brightness_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/
screen_brightness_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_macos-0.1.0+1/
screen_brightness_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_windows-0.1.3/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
vibration=/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-1.9.0/
wakelock_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/
