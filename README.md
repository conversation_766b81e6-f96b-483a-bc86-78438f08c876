# 오픈시스템즈 사용자용 모바일 앱

실시간 트레이딩 신호를 수신하는 사용자용 모바일 애플리케이션입니다.

## 📱 주요 기능

### 🔔 실시간 신호 수신
- WebSocket을 통한 실시간 신호 수신
- 매수(LONG) / 매도(SHORT) 신호 구분
- 즉시 알림 및 진동
- 화면 자동 깨우기

### 🎵 알림 시스템
- 커스터마이징 가능한 알림 소리
- 진동 패턴 설정
- 볼륨 조절
- 테스트 알림 기능

### 🔄 백그라운드 실행
- 앱이 백그라운드에 있어도 신호 수신
- 자동 재연결 기능
- 배터리 최적화 제외 설정

### 📊 히스토리 및 통계
- 수신한 신호 히스토리 저장
- 매수/매도 신호 통계
- 필터링 및 검색 기능

### ⚙️ 설정 관리
- 서버 URL 설정
- 알림 설정 커스터마이징
- 데이터 관리 기능

## 🚀 설치 및 실행

### 요구사항
- Flutter SDK 3.10.0 이상
- Android Studio (Android 빌드용)
- Android 7.0 (API 24) 이상

### 설치 방법

1. **의존성 설치**
```bash
cd opensystems_user_mobile
flutter pub get
```

2. **Android APK 빌드**
```bash
flutter build apk --release
```

3. **APK 설치**
- `build/app/outputs/flutter-apk/app-release.apk` 파일을 Android 기기에 설치

### 개발 모드 실행
```bash
flutter run
```

## 📋 사용법

### 1. 초기 설정
1. 앱 실행 후 '연결 설정' 탭으로 이동
2. 서버 URL 입력 (예: `ws://192.168.1.100:8080/ws`)
3. '저장' 버튼 클릭
4. '연결' 버튼으로 서버 연결

### 2. 신호 수신
1. '신호 수신' 탭에서 연결 상태 확인
2. 서버에서 신호 발생 시 자동으로 알림 수신
3. 알림 확인 후 신호 정보 표시

### 3. 설정 조정
1. '설정' 탭에서 알림 설정 조정
2. 사운드, 진동, 화면 깨우기 등 설정
3. 백그라운드 서비스 활성화

### 4. 히스토리 확인
1. '히스토리' 탭에서 수신한 신호 확인
2. 필터링으로 매수/매도 신호 구분
3. 상세 정보 확인 가능

## 🔧 기술 스택

- **Framework**: Flutter 3.x
- **Language**: Dart 3.x
- **State Management**: Provider
- **Network**: WebSocket (web_socket_channel)
- **Local Storage**: SharedPreferences, FlutterSecureStorage
- **Notifications**: FlutterLocalNotifications
- **Background Service**: FlutterBackgroundService
- **Audio**: JustAudio
- **UI**: FlutterScreenUtil, Material 3

## 📁 프로젝트 구조

```
lib/
├── main.dart                    # 앱 진입점
├── models/
│   └── trading_signal.dart     # 트레이딩 신호 모델
├── services/
│   ├── websocket_service.dart   # WebSocket 통신
│   ├── notification_service.dart # 알림 서비스
│   ├── storage_service.dart     # 로컬 저장소
│   └── background_service.dart  # 백그라운드 서비스
└── screens/
    ├── signal_receiver_screen.dart # 신호 수신 화면
    ├── connection_screen.dart      # 연결 설정 화면
    ├── history_screen.dart         # 히스토리 화면
    └── settings_screen.dart        # 설정 화면
```

## 🔐 권한 설정

앱이 정상적으로 작동하려면 다음 권한이 필요합니다:

- **인터넷 접근**: 서버 연결용
- **알림**: 신호 알림 표시
- **진동**: 신호 수신 시 진동
- **백그라운드 실행**: 백그라운드에서 신호 수신
- **화면 깨우기**: 신호 수신 시 화면 켜기
- **배터리 최적화 제외**: 백그라운드 실행 보장

## 🔄 서버 연동

### 관리자 서버 설정
1. 관리자 웹앱이 포트 8080에서 실행 중이어야 함
2. WebSocket 엔드포인트: `/ws`
3. 신호 메시지 형식:
```json
{
  "type": "signal",
  "signalType": "LONG",
  "symbol": "BTCUSDT",
  "timestamp": "2025-01-01T12:00:00Z"
}
```

### 네트워크 설정
- 같은 WiFi 네트워크에 연결
- 방화벽에서 포트 8080 허용
- 모바일 데이터 사용 시 포트 포워딩 설정

## 🐛 문제 해결

### 연결 문제
1. 서버 URL 확인
2. 네트워크 연결 상태 확인
3. 방화벽 설정 확인
4. 관리자 서버 실행 상태 확인

### 알림 문제
1. 알림 권한 허용 확인
2. 배터리 최적화 제외 설정
3. 방해 금지 모드 해제
4. 앱 알림 설정 확인

### 백그라운드 실행 문제
1. 백그라운드 앱 새로고침 허용
2. 자동 시작 관리자에서 앱 허용
3. 배터리 최적화 제외
4. 백그라운드 서비스 활성화

## 📞 지원

### 개발 환경
- Flutter SDK: 3.10.0+
- Dart: 3.0.0+
- Android: 7.0+ (API 24+)

### 문의
- 개발사: 오픈시스템즈
- 이메일: <EMAIL>

---

**© 2025 오픈시스템즈. 모든 권리 보유.**
