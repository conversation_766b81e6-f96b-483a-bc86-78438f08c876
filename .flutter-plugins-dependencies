{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_background_service_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_ios-5.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.44/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "vibration", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-1.9.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "android": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_background_service_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_android-6.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.44/", "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_android-0.1.0+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "vibration", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vibration-1.9.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "macos": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.44/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_macos-0.1.0+1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "linux": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": false, "dependencies": ["package_info_plus"], "dev_dependency": false}], "windows": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "screen_brightness_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/screen_brightness_windows-0.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": false, "dependencies": ["package_info_plus"], "dev_dependency": false}], "web": [{"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/", "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": [], "dev_dependency": false}, {"name": "just_audio_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.13/", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "dependencies": ["package_info_plus"], "dev_dependency": false}]}, "dependencyGraph": [{"name": "audio_session", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "flutter_background_service", "dependencies": ["flutter_background_service_android", "flutter_background_service_ios"]}, {"name": "flutter_background_service_android", "dependencies": []}, {"name": "flutter_background_service_ios", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "just_audio", "dependencies": ["just_audio_web", "audio_session", "path_provider"]}, {"name": "just_audio_web", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "screen_brightness", "dependencies": ["screen_brightness_android", "screen_brightness_ios", "screen_brightness_macos", "screen_brightness_windows"]}, {"name": "screen_brightness_android", "dependencies": []}, {"name": "screen_brightness_ios", "dependencies": []}, {"name": "screen_brightness_macos", "dependencies": []}, {"name": "screen_brightness_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "vibration", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}], "date_created": "2025-06-26 20:12:42.868817", "version": "3.29.3", "swift_package_manager_enabled": {"ios": false, "macos": false}}