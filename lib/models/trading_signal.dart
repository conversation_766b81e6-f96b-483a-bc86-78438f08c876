import 'package:intl/intl.dart';

class TradingSignal {
  final int id;
  final String signalType; // 'LONG', 'SHORT'
  final String symbol; // 'BTCUSDT', 'ETHUSDT' 등
  final DateTime detectedAt;
  final String source; // 'LogFile', 'Manual', 'API' 등
  final String? rawLogLine;
  final String? targetApp; // 신호를 전송할 대상 앱
  final String? response; // 전송 응답
  final DateTime? sentAt; // 전송 시간
  final Map<String, dynamic>? metadata; // 추가 메타데이터

  TradingSignal({
    required this.id,
    required this.signalType,
    required this.symbol,
    required this.detectedAt,
    required this.source,
    this.rawLogLine,
    this.targetApp,
    this.response,
    this.sentAt,
    this.metadata,
  });

  // JSON 직렬화
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'signalType': signalType,
      'symbol': symbol,
      'detectedAt': detectedAt.toIso8601String(),
      'source': source,
      'rawLogLine': rawLogLine,
      'targetApp': targetApp,
      'response': response,
      'sentAt': sentAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  // JSON 역직렬화
  factory TradingSignal.fromJson(Map<String, dynamic> json) {
    return TradingSignal(
      id: json['id'] ?? DateTime.now().millisecondsSinceEpoch,
      signalType: json['signalType'] ?? json['type'] ?? 'UNKNOWN',
      symbol: json['symbol'] ?? json['coin'] ?? 'UNKNOWN',
      detectedAt: json['detectedAt'] != null 
          ? DateTime.parse(json['detectedAt'])
          : (json['timestamp'] != null 
              ? DateTime.parse(json['timestamp'])
              : DateTime.now()),
      source: json['source'] ?? 'WebSocket',
      rawLogLine: json['rawLogLine'],
      targetApp: json['targetApp'],
      response: json['response'],
      sentAt: json['sentAt'] != null ? DateTime.parse(json['sentAt']) : null,
      metadata: json['metadata'],
    );
  }

  // 복사 생성자
  TradingSignal copyWith({
    int? id,
    String? signalType,
    String? symbol,
    DateTime? detectedAt,
    String? source,
    String? rawLogLine,
    String? targetApp,
    String? response,
    DateTime? sentAt,
    Map<String, dynamic>? metadata,
  }) {
    return TradingSignal(
      id: id ?? this.id,
      signalType: signalType ?? this.signalType,
      symbol: symbol ?? this.symbol,
      detectedAt: detectedAt ?? this.detectedAt,
      source: source ?? this.source,
      rawLogLine: rawLogLine ?? this.rawLogLine,
      targetApp: targetApp ?? this.targetApp,
      response: response ?? this.response,
      sentAt: sentAt ?? this.sentAt,
      metadata: metadata ?? this.metadata,
    );
  }

  // 신호 타입에 따른 색상
  String get signalColor {
    switch (signalType.toUpperCase()) {
      case 'LONG':
        return '#4CAF50'; // 녹색
      case 'SHORT':
        return '#F44336'; // 빨간색
      default:
        return '#9E9E9E'; // 회색
    }
  }

  // 신호 타입에 따른 아이콘
  String get signalIcon {
    switch (signalType.toUpperCase()) {
      case 'LONG':
        return '📈'; // 상승 차트
      case 'SHORT':
        return '📉'; // 하락 차트
      default:
        return '📊'; // 일반 차트
    }
  }

  // 신호 타입 한글 표시
  String get signalTypeKorean {
    switch (signalType.toUpperCase()) {
      case 'LONG':
        return '매수';
      case 'SHORT':
        return '매도';
      default:
        return '알 수 없음';
    }
  }

  // 시간 포맷팅
  String get formattedTime {
    final formatter = DateFormat('MM/dd HH:mm:ss');
    return formatter.format(detectedAt);
  }

  // 상세 시간 포맷팅
  String get detailedFormattedTime {
    final formatter = DateFormat('yyyy년 MM월 dd일 HH:mm:ss');
    return formatter.format(detectedAt);
  }

  // 경과 시간 계산
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(detectedAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}일 전';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}시간 전';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}분 전';
    } else {
      return '방금 전';
    }
  }

  // 심볼 포맷팅 (USDT 제거)
  String get formattedSymbol {
    if (symbol.endsWith('USDT')) {
      return symbol.substring(0, symbol.length - 4);
    }
    return symbol;
  }

  // 알림 제목 생성
  String get notificationTitle {
    return '$signalIcon $signalTypeKorean 신호';
  }

  // 알림 내용 생성
  String get notificationBody {
    return '$formattedSymbol - ${formattedTime}';
  }

  // 신호 요약 정보
  String get summary {
    return '$signalTypeKorean $formattedSymbol (${formattedTime})';
  }

  @override
  String toString() {
    return 'TradingSignal{id: $id, signalType: $signalType, symbol: $symbol, detectedAt: $detectedAt}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TradingSignal &&
        other.id == id &&
        other.signalType == signalType &&
        other.symbol == symbol &&
        other.detectedAt == detectedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        signalType.hashCode ^
        symbol.hashCode ^
        detectedAt.hashCode;
  }
}
