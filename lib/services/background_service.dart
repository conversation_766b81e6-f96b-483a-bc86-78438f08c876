import 'dart:async';
import 'dart:convert';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_background_service_android/flutter_background_service_android.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/trading_signal.dart';

class BackgroundService {
  static const String _serviceName = 'opensystems_signal_service';
  
  static Future<void> init() async {
    final service = FlutterBackgroundService();
    
    // Android 설정
    await service.configure(
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        autoStart: true,
        isForegroundMode: true,
        notificationChannelId: 'background_service',
        initialNotificationTitle: '오픈시스템즈 신호 수신',
        initialNotificationContent: '백그라운드에서 신호를 수신 중입니다',
        foregroundServiceNotificationId: 888,
      ),
      iosConfiguration: IosConfiguration(
        autoStart: true,
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
    );
  }

  static Future<void> startService() async {
    final service = FlutterBackgroundService();
    
    if (await service.isRunning()) {
      debugPrint('[BackgroundService] 이미 실행 중');
      return;
    }
    
    await service.startService();
    debugPrint('[BackgroundService] 서비스 시작됨');
  }

  static Future<void> stopService() async {
    final service = FlutterBackgroundService();
    await service.invoke('stop');
    debugPrint('[BackgroundService] 서비스 중지됨');
  }

  static Future<bool> isRunning() async {
    final service = FlutterBackgroundService();
    return await service.isRunning();
  }

  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    DartPluginRegistrant.ensureInitialized();
    
    if (service is AndroidServiceInstance) {
      service.on('setAsForeground').listen((event) {
        service.setAsForegroundService();
      });
      
      service.on('setAsBackground').listen((event) {
        service.setAsBackgroundService();
      });
    }
    
    service.on('stop').listen((event) {
      service.stopSelf();
    });
    
    // WebSocket 연결 및 신호 수신 로직
    await _startSignalMonitoring(service);
  }

  @pragma('vm:entry-point')
  static Future<bool> onIosBackground(ServiceInstance service) async {
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();
    
    await _startSignalMonitoring(service);
    return true;
  }

  static Future<void> _startSignalMonitoring(ServiceInstance service) async {
    final prefs = await SharedPreferences.getInstance();
    final serverUrl = prefs.getString('server_url') ?? 'ws://localhost:8080/ws';
    
    WebSocketChannel? channel;
    Timer? reconnectTimer;
    Timer? heartbeatTimer;
    
    Future<void> connectWebSocket() async {
      try {
        debugPrint('[BackgroundService] WebSocket 연결 시도: $serverUrl');
        
        channel = WebSocketChannel.connect(Uri.parse(serverUrl));
        await channel!.ready;
        
        debugPrint('[BackgroundService] WebSocket 연결 성공');
        
        // 하트비트 시작
        heartbeatTimer?.cancel();
        heartbeatTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
          try {
            channel?.sink.add(json.encode({'type': 'ping'}));
          } catch (e) {
            debugPrint('[BackgroundService] 하트비트 전송 오류: $e');
            timer.cancel();
            connectWebSocket();
          }
        });
        
        // 메시지 리스너
        channel!.stream.listen(
          (data) async {
            try {
              final message = json.decode(data);
              if (message['type'] == 'signal') {
                await _handleSignalInBackground(message, service);
              }
            } catch (e) {
              debugPrint('[BackgroundService] 메시지 처리 오류: $e');
            }
          },
          onError: (error) {
            debugPrint('[BackgroundService] WebSocket 오류: $error');
            _scheduleReconnect();
          },
          onDone: () {
            debugPrint('[BackgroundService] WebSocket 연결 종료');
            _scheduleReconnect();
          },
        );
        
        // 재연결 타이머 정리
        reconnectTimer?.cancel();
        
      } catch (e) {
        debugPrint('[BackgroundService] WebSocket 연결 실패: $e');
        _scheduleReconnect();
      }
    }
    
    void _scheduleReconnect() {
      reconnectTimer?.cancel();
      reconnectTimer = Timer(const Duration(seconds: 5), () {
        connectWebSocket();
      });
    }
    
    // 초기 연결
    await connectWebSocket();
    
    // 서비스 상태 업데이트 타이머
    Timer.periodic(const Duration(seconds: 10), (timer) async {
      if (service is AndroidServiceInstance) {
        final now = DateTime.now();
        await service.setForegroundNotificationInfo(
          title: '오픈시스템즈 신호 수신',
          content: '마지막 업데이트: ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}',
        );
      }
    });
    
    // 서비스 종료 시 정리
    service.on('stop').listen((event) {
      heartbeatTimer?.cancel();
      reconnectTimer?.cancel();
      channel?.sink.close();
    });
  }

  static Future<void> _handleSignalInBackground(
    Map<String, dynamic> message,
    ServiceInstance service,
  ) async {
    try {
      final signal = TradingSignal.fromJson(message);
      
      debugPrint('[BackgroundService] 신호 수신: ${signal.signalType} ${signal.symbol}');
      
      // 알림 표시
      await _showBackgroundNotification(signal);
      
      // 신호 히스토리 저장
      await _saveSignalToHistory(signal);
      
      // 통계 업데이트
      await _updateStatistics(signal);
      
      // 서비스 알림 업데이트
      if (service is AndroidServiceInstance) {
        await service.setForegroundNotificationInfo(
          title: '새 신호 수신: ${signal.signalTypeKorean}',
          content: '${signal.formattedSymbol} - ${signal.formattedTime}',
        );
      }
      
    } catch (e) {
      debugPrint('[BackgroundService] 신호 처리 오류: $e');
    }
  }

  static Future<void> _showBackgroundNotification(TradingSignal signal) async {
    final notifications = FlutterLocalNotificationsPlugin();
    
    const androidDetails = AndroidNotificationDetails(
      'trading_signals_bg',
      '백그라운드 트레이딩 신호',
      channelDescription: '백그라운드에서 수신된 트레이딩 신호',
      importance: Importance.max,
      priority: Priority.high,
      enableVibration: true,
      enableLights: true,
      ledColor: Color(0xFF2196F3),
      fullScreenIntent: true,
      category: AndroidNotificationCategory.alarm,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    await notifications.show(
      signal.id,
      signal.notificationTitle,
      signal.notificationBody,
      details,
    );
  }

  static Future<void> _saveSignalToHistory(TradingSignal signal) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('signal_history') ?? '[]';
      final List<dynamic> history = json.decode(historyJson);
      
      history.insert(0, signal.toJson());
      
      // 최근 100개만 유지
      if (history.length > 100) {
        history.removeRange(100, history.length);
      }
      
      await prefs.setString('signal_history', json.encode(history));
    } catch (e) {
      debugPrint('[BackgroundService] 히스토리 저장 오류: $e');
    }
  }

  static Future<void> _updateStatistics(TradingSignal signal) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final totalSignals = (prefs.getInt('total_signals') ?? 0) + 1;
      await prefs.setInt('total_signals', totalSignals);
      
      if (signal.signalType.toUpperCase() == 'LONG') {
        final longSignals = (prefs.getInt('long_signals') ?? 0) + 1;
        await prefs.setInt('long_signals', longSignals);
      } else if (signal.signalType.toUpperCase() == 'SHORT') {
        final shortSignals = (prefs.getInt('short_signals') ?? 0) + 1;
        await prefs.setInt('short_signals', shortSignals);
      }
      
      await prefs.setString('last_signal_time', signal.detectedAt.toIso8601String());
    } catch (e) {
      debugPrint('[BackgroundService] 통계 업데이트 오류: $e');
    }
  }
}
