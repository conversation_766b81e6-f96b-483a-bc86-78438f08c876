import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../models/trading_signal.dart';
import 'notification_service.dart';
import 'storage_service.dart';

class WebSocketService extends ChangeNotifier {
  WebSocketChannel? _channel;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;
  
  bool _isConnected = false;
  bool _isConnecting = false;
  String _connectionStatus = '연결 안됨';
  DateTime? _lastSignalTime;
  TradingSignal? _lastSignal;
  int _reconnectAttempts = 0;
  
  // 연결 설정
  String _serverUrl = 'ws://localhost:8080/ws';
  static const Duration _reconnectInterval = Duration(seconds: 5);
  static const Duration _heartbeatInterval = Duration(seconds: 30);
  static const int _maxReconnectAttempts = 10;
  
  // 통계
  int _totalSignalsReceived = 0;
  int _longSignalsReceived = 0;
  int _shortSignalsReceived = 0;
  
  // Getters
  bool get isConnected => _isConnected;
  bool get isConnecting => _isConnecting;
  String get connectionStatus => _connectionStatus;
  DateTime? get lastSignalTime => _lastSignalTime;
  TradingSignal? get lastSignal => _lastSignal;
  String get serverUrl => _serverUrl;
  int get totalSignalsReceived => _totalSignalsReceived;
  int get longSignalsReceived => _longSignalsReceived;
  int get shortSignalsReceived => _shortSignalsReceived;
  int get reconnectAttempts => _reconnectAttempts;

  WebSocketService() {
    _loadSettings();
    _loadStatistics();
  }

  Future<void> _loadSettings() async {
    final savedUrl = await StorageService.getString('server_url');
    if (savedUrl != null && savedUrl.isNotEmpty) {
      _serverUrl = savedUrl;
    }
  }

  Future<void> _loadStatistics() async {
    _totalSignalsReceived = await StorageService.getInt('total_signals') ?? 0;
    _longSignalsReceived = await StorageService.getInt('long_signals') ?? 0;
    _shortSignalsReceived = await StorageService.getInt('short_signals') ?? 0;
  }

  Future<void> updateServerUrl(String url) async {
    _serverUrl = url;
    await StorageService.setString('server_url', url);
    
    // 연결 중이면 재연결
    if (_isConnected) {
      await disconnect();
      await connect();
    }
  }

  Future<void> connect() async {
    if (_isConnecting || _isConnected) return;
    
    _isConnecting = true;
    _connectionStatus = '연결 중...';
    notifyListeners();
    
    try {
      debugPrint('[WebSocket] 연결 시도 ${_reconnectAttempts + 1}/$_maxReconnectAttempts: $_serverUrl');
      
      _channel = WebSocketChannel.connect(Uri.parse(_serverUrl));
      
      // 연결 성공 대기 (타임아웃 10초)
      await _channel!.ready.timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('연결 타임아웃'),
      );
      
      _isConnected = true;
      _isConnecting = false;
      _connectionStatus = '연결됨';
      _reconnectAttempts = 0;
      
      debugPrint('[WebSocket] 연결 성공');
      
      // 메시지 리스너 설정
      _setupMessageListener();
      
      // 하트비트 시작
      _startHeartbeat();
      
      // 재연결 타이머 정리
      _reconnectTimer?.cancel();
      _reconnectTimer = null;
      
      notifyListeners();
      
    } catch (e) {
      debugPrint('[WebSocket] 연결 실패: $e');
      _handleConnectionError();
    }
  }

  void _setupMessageListener() {
    _channel?.stream.listen(
      (data) {
        try {
          final message = json.decode(data);
          _handleMessage(message);
        } catch (e) {
          debugPrint('[WebSocket] 메시지 파싱 오류: $e');
        }
      },
      onError: (error) {
        debugPrint('[WebSocket] 스트림 오류: $error');
        _handleConnectionError();
      },
      onDone: () {
        debugPrint('[WebSocket] 연결 종료됨');
        _handleConnectionError();
      },
    );
  }

  void _handleMessage(Map<String, dynamic> message) {
    debugPrint('[WebSocket] 메시지 수신: $message');
    
    try {
      // 신호 메시지 처리
      if (message['type'] == 'signal') {
        final signal = TradingSignal.fromJson(message);
        _lastSignal = signal;
        _lastSignalTime = DateTime.now();
        
        // 통계 업데이트
        _updateStatistics(signal);
        
        // 알림 표시
        NotificationService.showSignalNotification(signal);
        
        // 신호 저장
        _saveSignalHistory(signal);
        
        notifyListeners();
      }
      // 하트비트 응답
      else if (message['type'] == 'pong') {
        debugPrint('[WebSocket] 하트비트 응답 수신');
      }
    } catch (e) {
      debugPrint('[WebSocket] 메시지 처리 오류: $e');
    }
  }

  Future<void> _updateStatistics(TradingSignal signal) async {
    _totalSignalsReceived++;
    
    if (signal.signalType.toUpperCase() == 'LONG') {
      _longSignalsReceived++;
    } else if (signal.signalType.toUpperCase() == 'SHORT') {
      _shortSignalsReceived++;
    }
    
    // 통계 저장
    await StorageService.setInt('total_signals', _totalSignalsReceived);
    await StorageService.setInt('long_signals', _longSignalsReceived);
    await StorageService.setInt('short_signals', _shortSignalsReceived);
  }

  Future<void> _saveSignalHistory(TradingSignal signal) async {
    try {
      final history = await getSignalHistory();
      history.insert(0, signal);
      
      // 최근 100개만 유지
      if (history.length > 100) {
        history.removeRange(100, history.length);
      }
      
      final historyJson = history.map((s) => s.toJson()).toList();
      await StorageService.setString('signal_history', json.encode(historyJson));
    } catch (e) {
      debugPrint('[WebSocket] 신호 히스토리 저장 오류: $e');
    }
  }

  Future<List<TradingSignal>> getSignalHistory() async {
    try {
      final historyJson = await StorageService.getString('signal_history');
      if (historyJson == null) return [];
      
      final List<dynamic> historyList = json.decode(historyJson);
      return historyList.map((json) => TradingSignal.fromJson(json)).toList();
    } catch (e) {
      debugPrint('[WebSocket] 신호 히스토리 로드 오류: $e');
      return [];
    }
  }

  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) {
      if (_isConnected && _channel != null) {
        try {
          _channel!.sink.add(json.encode({'type': 'ping'}));
        } catch (e) {
          debugPrint('[WebSocket] 하트비트 전송 오류: $e');
          _handleConnectionError();
        }
      }
    });
  }

  void _handleConnectionError() {
    _isConnected = false;
    _isConnecting = false;
    _connectionStatus = '연결 끊김';
    
    _heartbeatTimer?.cancel();
    _channel?.sink.close(status.goingAway);
    _channel = null;
    
    notifyListeners();
    
    // 자동 재연결 시도
    _scheduleReconnect();
  }

  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      _connectionStatus = '재연결 포기 (최대 시도 횟수 초과)';
      notifyListeners();
      return;
    }
    
    _reconnectAttempts++;
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(_reconnectInterval, () {
      if (!_isConnected && !_isConnecting) {
        debugPrint('[WebSocket] 자동 재연결 시도 $_reconnectAttempts/$_maxReconnectAttempts');
        connect();
      }
    });
  }

  Future<void> manualReconnect() async {
    _reconnectAttempts = 0;
    await disconnect();
    await connect();
  }

  Future<void> disconnect() async {
    _reconnectTimer?.cancel();
    _heartbeatTimer?.cancel();
    
    if (_channel != null) {
      await _channel!.sink.close(status.goingAway);
      _channel = null;
    }
    
    _isConnected = false;
    _isConnecting = false;
    _connectionStatus = '연결 안됨';
    
    notifyListeners();
  }

  Future<void> clearStatistics() async {
    _totalSignalsReceived = 0;
    _longSignalsReceived = 0;
    _shortSignalsReceived = 0;
    
    await StorageService.setInt('total_signals', 0);
    await StorageService.setInt('long_signals', 0);
    await StorageService.setInt('short_signals', 0);
    
    notifyListeners();
  }

  @override
  void dispose() {
    disconnect();
    super.dispose();
  }
}
