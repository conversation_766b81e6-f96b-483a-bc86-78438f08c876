import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class StorageService {
  static SharedPreferences? _prefs;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // String 값 저장/조회
  static Future<void> setString(String key, String value) async {
    await _prefs?.setString(key, value);
  }

  static String? getString(String key) {
    return _prefs?.getString(key);
  }

  // Bool 값 저장/조회
  static Future<void> setBool(String key, bool value) async {
    await _prefs?.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _prefs?.getBool(key);
  }

  // Int 값 저장/조회
  static Future<void> setInt(String key, int value) async {
    await _prefs?.setInt(key, value);
  }

  static int? getInt(String key) {
    return _prefs?.getInt(key);
  }

  // Double 값 저장/조회
  static Future<void> setDouble(String key, double value) async {
    await _prefs?.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return _prefs?.getDouble(key);
  }

  // StringList 값 저장/조회
  static Future<void> setStringList(String key, List<String> value) async {
    await _prefs?.setStringList(key, value);
  }

  static List<String>? getStringList(String key) {
    return _prefs?.getStringList(key);
  }

  // 키 삭제
  static Future<void> remove(String key) async {
    await _prefs?.remove(key);
  }

  // 모든 데이터 삭제
  static Future<void> clear() async {
    await _prefs?.clear();
  }

  // 키 존재 여부 확인
  static bool containsKey(String key) {
    return _prefs?.containsKey(key) ?? false;
  }

  // 모든 키 조회
  static Set<String> getKeys() {
    return _prefs?.getKeys() ?? {};
  }

  // 보안 저장소 - 민감한 정보 저장용
  static Future<void> setSecureString(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  static Future<String?> getSecureString(String key) async {
    return await _secureStorage.read(key: key);
  }

  static Future<void> deleteSecureString(String key) async {
    await _secureStorage.delete(key: key);
  }

  static Future<void> clearSecureStorage() async {
    await _secureStorage.deleteAll();
  }

  // 앱 설정 관련 편의 메서드들
  static Future<void> setFirstLaunch(bool isFirst) async {
    await setBool('first_launch', isFirst);
  }

  static bool isFirstLaunch() {
    return getBool('first_launch') ?? true;
  }

  static Future<void> setServerUrl(String url) async {
    await setString('server_url', url);
  }

  static String getServerUrl() {
    return getString('server_url') ?? 'ws://localhost:8080/ws';
  }

  static Future<void> setNotificationEnabled(bool enabled) async {
    await setBool('notification_enabled', enabled);
  }

  static bool isNotificationEnabled() {
    return getBool('notification_enabled') ?? true;
  }

  static Future<void> setSoundEnabled(bool enabled) async {
    await setBool('sound_enabled', enabled);
  }

  static bool isSoundEnabled() {
    return getBool('sound_enabled') ?? true;
  }

  static Future<void> setVibrationEnabled(bool enabled) async {
    await setBool('vibration_enabled', enabled);
  }

  static bool isVibrationEnabled() {
    return getBool('vibration_enabled') ?? true;
  }

  static Future<void> setWakelockEnabled(bool enabled) async {
    await setBool('wakelock_enabled', enabled);
  }

  static bool isWakelockEnabled() {
    return getBool('wakelock_enabled') ?? true;
  }

  static Future<void> setVolume(double volume) async {
    await setDouble('volume', volume);
  }

  static double getVolume() {
    return getDouble('volume') ?? 1.0;
  }

  static Future<void> setAutoReconnect(bool enabled) async {
    await setBool('auto_reconnect', enabled);
  }

  static bool isAutoReconnectEnabled() {
    return getBool('auto_reconnect') ?? true;
  }

  static Future<void> setBackgroundService(bool enabled) async {
    await setBool('background_service', enabled);
  }

  static bool isBackgroundServiceEnabled() {
    return getBool('background_service') ?? true;
  }

  // 통계 관련
  static Future<void> setTotalSignals(int count) async {
    await setInt('total_signals', count);
  }

  static int getTotalSignals() {
    return getInt('total_signals') ?? 0;
  }

  static Future<void> setLongSignals(int count) async {
    await setInt('long_signals', count);
  }

  static int getLongSignals() {
    return getInt('long_signals') ?? 0;
  }

  static Future<void> setShortSignals(int count) async {
    await setInt('short_signals', count);
  }

  static int getShortSignals() {
    return getInt('short_signals') ?? 0;
  }

  static Future<void> setLastSignalTime(DateTime time) async {
    await setString('last_signal_time', time.toIso8601String());
  }

  static DateTime? getLastSignalTime() {
    final timeString = getString('last_signal_time');
    return timeString != null ? DateTime.parse(timeString) : null;
  }

  // 앱 버전 관리
  static Future<void> setAppVersion(String version) async {
    await setString('app_version', version);
  }

  static String? getAppVersion() {
    return getString('app_version');
  }

  // 디버그 모드
  static Future<void> setDebugMode(bool enabled) async {
    await setBool('debug_mode', enabled);
  }

  static bool isDebugMode() {
    return getBool('debug_mode') ?? false;
  }
}
