import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:just_audio/just_audio.dart';
import 'package:vibration/vibration.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../models/trading_signal.dart';
import 'storage_service.dart';

class NotificationService extends ChangeNotifier {
  static final FlutterLocalNotificationsPlugin _notifications = 
      FlutterLocalNotificationsPlugin();
  static AudioPlayer? _audioPlayer;
  
  // 설정
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _wakelockEnabled = true;
  double _volume = 1.0;
  String _soundFile = 'notification.mp3';
  
  // Getters
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;
  bool get wakelockEnabled => _wakelockEnabled;
  double get volume => _volume;
  String get soundFile => _soundFile;

  static Future<void> init() async {
    // Android 초기화 설정
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    
    // iOS 초기화 설정
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
    
    // Android 알림 채널 생성
    await _createNotificationChannel();
    
    // 오디오 플레이어 초기화
    _audioPlayer = AudioPlayer();
  }

  static Future<void> _createNotificationChannel() async {
    const androidChannel = AndroidNotificationChannel(
      'trading_signals',
      '트레이딩 신호',
      description: '트레이딩 신호 알림',
      importance: Importance.max,
      priority: Priority.high,
      enableVibration: true,
      enableLights: true,
      ledColor: Color(0xFF2196F3),
      showBadge: true,
    );
    
    await _notifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('[Notification] 알림 탭됨: ${response.payload}');
    // 필요시 특정 화면으로 이동하는 로직 추가
  }

  static Future<void> showSignalNotification(TradingSignal signal) async {
    try {
      // 알림 표시
      await _showNotification(signal);
      
      // 사운드 재생
      await _playSound();
      
      // 진동
      await _vibrate();
      
      // 화면 깨우기
      await _wakeScreen();
      
    } catch (e) {
      debugPrint('[Notification] 알림 표시 오류: $e');
    }
  }

  static Future<void> _showNotification(TradingSignal signal) async {
    const androidDetails = AndroidNotificationDetails(
      'trading_signals',
      '트레이딩 신호',
      channelDescription: '트레이딩 신호 알림',
      importance: Importance.max,
      priority: Priority.high,
      ticker: '새로운 트레이딩 신호',
      icon: '@mipmap/ic_launcher',
      largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
      enableVibration: true,
      enableLights: true,
      ledColor: Color(0xFF2196F3),
      ledOnMs: 1000,
      ledOffMs: 500,
      showWhen: true,
      when: null,
      usesChronometer: false,
      chronometerCountDown: false,
      channelShowBadge: true,
      onlyAlertOnce: false,
      ongoing: false,
      autoCancel: true,
      silent: false,
      fullScreenIntent: true,
      category: AndroidNotificationCategory.alarm,
      visibility: NotificationVisibility.public,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'notification.aiff',
      badgeNumber: 1,
      threadIdentifier: 'trading_signals',
      categoryIdentifier: 'trading_signal_category',
    );
    
    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );
    
    await _notifications.show(
      signal.id,
      signal.notificationTitle,
      signal.notificationBody,
      details,
      payload: signal.toJson().toString(),
    );
  }

  static Future<void> _playSound() async {
    try {
      if (_audioPlayer != null) {
        await _audioPlayer!.setAsset('assets/sounds/notification.mp3');
        await _audioPlayer!.setVolume(1.0);
        await _audioPlayer!.play();
      }
    } catch (e) {
      debugPrint('[Notification] 사운드 재생 오류: $e');
    }
  }

  static Future<void> _vibrate() async {
    try {
      if (await Vibration.hasVibrator() ?? false) {
        // 강한 진동 패턴 (500ms 진동, 200ms 정지, 500ms 진동)
        await Vibration.vibrate(
          pattern: [0, 500, 200, 500],
          intensities: [0, 255, 0, 255],
        );
      }
    } catch (e) {
      debugPrint('[Notification] 진동 오류: $e');
    }
  }

  static Future<void> _wakeScreen() async {
    try {
      // 화면 깨우기 (5초간 유지)
      await WakelockPlus.enable();
      
      // 5초 후 자동으로 해제
      Future.delayed(const Duration(seconds: 5), () {
        WakelockPlus.disable();
      });
    } catch (e) {
      debugPrint('[Notification] 화면 깨우기 오류: $e');
    }
  }

  // 설정 로드
  Future<void> loadSettings() async {
    _soundEnabled = await StorageService.getBool('sound_enabled') ?? true;
    _vibrationEnabled = await StorageService.getBool('vibration_enabled') ?? true;
    _wakelockEnabled = await StorageService.getBool('wakelock_enabled') ?? true;
    _volume = await StorageService.getDouble('volume') ?? 1.0;
    _soundFile = await StorageService.getString('sound_file') ?? 'notification.mp3';
    notifyListeners();
  }

  // 사운드 설정
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await StorageService.setBool('sound_enabled', enabled);
    notifyListeners();
  }

  // 진동 설정
  Future<void> setVibrationEnabled(bool enabled) async {
    _vibrationEnabled = enabled;
    await StorageService.setBool('vibration_enabled', enabled);
    notifyListeners();
  }

  // 화면 깨우기 설정
  Future<void> setWakelockEnabled(bool enabled) async {
    _wakelockEnabled = enabled;
    await StorageService.setBool('wakelock_enabled', enabled);
    notifyListeners();
  }

  // 볼륨 설정
  Future<void> setVolume(double volume) async {
    _volume = volume;
    await StorageService.setDouble('volume', volume);
    notifyListeners();
  }

  // 사운드 파일 설정
  Future<void> setSoundFile(String soundFile) async {
    _soundFile = soundFile;
    await StorageService.setString('sound_file', soundFile);
    notifyListeners();
  }

  // 테스트 알림
  Future<void> showTestNotification() async {
    final testSignal = TradingSignal(
      id: DateTime.now().millisecondsSinceEpoch,
      signalType: 'LONG',
      symbol: 'BTCUSDT',
      detectedAt: DateTime.now(),
      source: 'Test',
    );
    
    await showSignalNotification(testSignal);
  }

  // 모든 알림 취소
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // 특정 알림 취소
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  @override
  void dispose() {
    _audioPlayer?.dispose();
    super.dispose();
  }
}
