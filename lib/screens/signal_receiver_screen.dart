import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../services/websocket_service.dart';
import '../services/notification_service.dart';
import '../models/trading_signal.dart';

class SignalReceiverScreen extends StatefulWidget {
  const SignalReceiverScreen({super.key});

  @override
  State<SignalReceiverScreen> createState() => _SignalReceiverScreenState();
}

class _SignalReceiverScreenState extends State<SignalReceiverScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _signalController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _signalAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _signalController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _signalAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _signalController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _signalController.dispose();
    super.dispose();
  }

  void _onSignalReceived() {
    _signalController.reset();
    _signalController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('신호 수신'),
        actions: [
          Consumer<WebSocketService>(
            builder: (context, webSocketService, child) {
              return IconButton(
                icon: Icon(
                  webSocketService.isConnected
                      ? Icons.wifi
                      : Icons.wifi_off,
                  color: webSocketService.isConnected
                      ? Colors.green
                      : Colors.red,
                ),
                onPressed: () {
                  if (webSocketService.isConnected) {
                    webSocketService.disconnect();
                  } else {
                    webSocketService.connect();
                  }
                },
              );
            },
          ),
        ],
      ),
      body: Consumer<WebSocketService>(
        builder: (context, webSocketService, child) {
          // 새 신호 수신 시 애니메이션 트리거
          if (webSocketService.lastSignal != null) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _onSignalReceived();
            });
          }
          
          return SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                // 연결 상태
                _buildConnectionStatus(webSocketService),
                SizedBox(height: 16.h),

                // 신호 수신 상태
                _buildSignalStatus(webSocketService),
                SizedBox(height: 16.h),

                // 최근 신호
                if (webSocketService.lastSignal != null)
                  _buildLastSignal(webSocketService.lastSignal!),

                SizedBox(height: 16.h),

                // 통계
                _buildStatistics(webSocketService),
                SizedBox(height: 16.h),

                // 테스트 버튼
                _buildTestButton(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildConnectionStatusCard(WebSocketService webSocketService) {
    final isConnected = webSocketService.isConnected;
    final isConnecting = webSocketService.isConnecting;
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: isConnected ? _pulseAnimation.value : 1.0,
                      child: Container(
                        width: 16.w,
                        height: 16.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isConnected
                              ? Colors.green
                              : isConnecting
                                  ? Colors.orange
                                  : Colors.red,
                        ),
                      ),
                    );
                  },
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '서버 연결 상태',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(
                        webSocketService.connectionStatus,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isConnected
                              ? Colors.green
                              : isConnecting
                                  ? Colors.orange
                                  : Colors.red,
                        ),
                      ),
                    ],
                  ),
                ),
                if (!isConnected && !isConnecting)
                  ElevatedButton(
                    onPressed: () => webSocketService.manualReconnect(),
                    child: const Text('재연결'),
                  ),
              ],
            ),
            if (webSocketService.reconnectAttempts > 0) ...[
              SizedBox(height: 8.h),
              LinearProgressIndicator(
                value: webSocketService.reconnectAttempts / 10,
                backgroundColor: Colors.grey[800],
                valueColor: AlwaysStoppedAnimation<Color>(
                  webSocketService.reconnectAttempts > 5
                      ? Colors.red
                      : Colors.orange,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                '재연결 시도: ${webSocketService.reconnectAttempts}/10',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSignalStatusCard(WebSocketService webSocketService) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.signal_cellular_alt,
                  size: 24.w,
                  color: webSocketService.isConnected
                      ? Colors.blue
                      : Colors.grey,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '신호 수신 대기',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(
                        webSocketService.isConnected
                            ? '신호를 수신할 준비가 되었습니다'
                            : '서버에 연결되지 않았습니다',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (webSocketService.lastSignalTime != null) ...[
              SizedBox(height: 12.h),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16.w,
                    color: Colors.grey,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    '마지막 신호: ${webSocketService.lastSignalTime!.toString().substring(11, 19)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLastSignalCard(TradingSignal signal) {
    return AnimatedBuilder(
      animation: _signalAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.9 + (0.1 * _signalAnimation.value),
          child: Card(
            color: signal.signalType.toUpperCase() == 'LONG'
                ? Colors.green.withValues(alpha: 0.1)
                : Colors.red.withValues(alpha: 0.1),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8.w),
                        decoration: BoxDecoration(
                          color: signal.signalType.toUpperCase() == 'LONG'
                              ? Colors.green
                              : Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          signal.signalIcon,
                          style: TextStyle(fontSize: 20.sp),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${signal.signalTypeKorean} 신호',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: signal.signalType.toUpperCase() == 'LONG'
                                    ? Colors.green
                                    : Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              signal.formattedSymbol,
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                            Text(
                              signal.detailedFormattedTime,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatisticsCard(WebSocketService webSocketService) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '수신 통계',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    '총 신호',
                    webSocketService.totalSignalsReceived.toString(),
                    Icons.signal_cellular_alt,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '매수 신호',
                    webSocketService.longSignalsReceived.toString(),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '매도 신호',
                    webSocketService.shortSignalsReceived.toString(),
                    Icons.trending_down,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24.w),
        SizedBox(height: 4.h),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTestButton() {
    return Consumer<NotificationService>(
      builder: (context, notificationService, child) {
        return ElevatedButton.icon(
          onPressed: () => notificationService.showTestNotification(),
          icon: const Icon(Icons.notifications),
          label: const Text('테스트 알림'),
        );
      },
    );
  }
}
