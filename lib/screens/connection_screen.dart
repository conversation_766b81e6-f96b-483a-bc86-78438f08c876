import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../services/websocket_service.dart';
import '../services/background_service.dart';

class ConnectionScreen extends StatefulWidget {
  const ConnectionScreen({super.key});

  @override
  State<ConnectionScreen> createState() => _ConnectionScreenState();
}

class _ConnectionScreenState extends State<ConnectionScreen> {
  final _urlController = TextEditingController();
  bool _isBackgroundServiceRunning = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _checkBackgroundService();
  }

  Future<void> _loadSettings() async {
    final webSocketService = context.read<WebSocketService>();
    _urlController.text = webSocketService.serverUrl;
  }

  Future<void> _checkBackgroundService() async {
    final isRunning = await BackgroundService.isRunning();
    setState(() {
      _isBackgroundServiceRunning = isRunning;
    });
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('연결 설정'),
      ),
      body: Consumer<WebSocketService>(
        builder: (context, webSocketService, child) {
          return SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                // 서버 URL 설정
                _buildServerUrlCard(webSocketService),
                SizedBox(height: 16.h),
                
                // 연결 상태 정보
                _buildConnectionInfoCard(webSocketService),
                SizedBox(height: 16.h),
                
                // 백그라운드 서비스 설정
                _buildBackgroundServiceCard(),
                SizedBox(height: 16.h),
                
                // 연결 제어 버튼
                _buildConnectionControlCard(webSocketService),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildServerUrlCard(WebSocketService webSocketService) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '서버 설정',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 12.h),
            TextField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: '서버 URL',
                hintText: 'ws://localhost:8080/ws',
                prefixIcon: Icon(Icons.link),
              ),
              keyboardType: TextInputType.url,
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () async {
                      await webSocketService.updateServerUrl(_urlController.text);
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('서버 URL이 저장되었습니다')),
                        );
                      }
                    },
                    child: const Text('저장'),
                  ),
                ),
                SizedBox(width: 8.w),
                ElevatedButton(
                  onPressed: () {
                    _urlController.text = 'ws://localhost:8080/ws';
                  },
                  child: const Text('기본값'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionInfoCard(WebSocketService webSocketService) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '연결 정보',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 12.h),
            _buildInfoRow('상태', webSocketService.connectionStatus),
            _buildInfoRow('서버 URL', webSocketService.serverUrl),
            if (webSocketService.lastSignalTime != null)
              _buildInfoRow(
                '마지막 신호',
                webSocketService.lastSignalTime!.toString().substring(0, 19),
              ),
            if (webSocketService.reconnectAttempts > 0)
              _buildInfoRow(
                '재연결 시도',
                '${webSocketService.reconnectAttempts}/10',
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundServiceCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '백그라운드 서비스',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Icon(
                  _isBackgroundServiceRunning
                      ? Icons.check_circle
                      : Icons.cancel,
                  color: _isBackgroundServiceRunning
                      ? Colors.green
                      : Colors.red,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    _isBackgroundServiceRunning
                        ? '백그라운드에서 실행 중'
                        : '백그라운드 서비스 중지됨',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                Switch(
                  value: _isBackgroundServiceRunning,
                  onChanged: (value) async {
                    if (value) {
                      await BackgroundService.startService();
                    } else {
                      await BackgroundService.stopService();
                    }
                    await _checkBackgroundService();
                  },
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              '백그라운드 서비스를 활성화하면 앱이 백그라운드에 있을 때도 신호를 수신할 수 있습니다.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionControlCard(WebSocketService webSocketService) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '연결 제어',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: webSocketService.isConnected
                        ? null
                        : () => webSocketService.connect(),
                    icon: const Icon(Icons.wifi),
                    label: const Text('연결'),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: webSocketService.isConnected
                        ? () => webSocketService.disconnect()
                        : null,
                    icon: const Icon(Icons.wifi_off),
                    label: const Text('연결 해제'),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => webSocketService.manualReconnect(),
                icon: const Icon(Icons.refresh),
                label: const Text('재연결'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
