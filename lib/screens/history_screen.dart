import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../services/websocket_service.dart';
import '../models/trading_signal.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  List<TradingSignal> _signals = [];
  String _filterType = 'ALL'; // ALL, LONG, SHORT
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSignalHistory();
  }

  Future<void> _loadSignalHistory() async {
    setState(() => _isLoading = true);
    
    final webSocketService = context.read<WebSocketService>();
    final signals = await webSocketService.getSignalHistory();
    
    setState(() {
      _signals = signals;
      _isLoading = false;
    });
  }

  List<TradingSignal> get _filteredSignals {
    if (_filterType == 'ALL') return _signals;
    return _signals.where((signal) => 
        signal.signalType.toUpperCase() == _filterType).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('신호 히스토리'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSignalHistory,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list),
            onSelected: (value) {
              setState(() => _filterType = value);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'ALL', child: Text('전체')),
              const PopupMenuItem(value: 'LONG', child: Text('매수만')),
              const PopupMenuItem(value: 'SHORT', child: Text('매도만')),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // 통계 요약
          _buildStatsSummary(),
          
          // 필터 칩
          _buildFilterChips(),
          
          // 신호 목록
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredSignals.isEmpty
                    ? _buildEmptyState()
                    : _buildSignalList(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSummary() {
    final totalSignals = _signals.length;
    final longSignals = _signals.where((s) => s.signalType.toUpperCase() == 'LONG').length;
    final shortSignals = _signals.where((s) => s.signalType.toUpperCase() == 'SHORT').length;

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem('총 신호', totalSignals.toString(), Colors.blue),
          ),
          Expanded(
            child: _buildStatItem('매수', longSignals.toString(), Colors.green),
          ),
          Expanded(
            child: _buildStatItem('매도', shortSignals.toString(), Colors.red),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          _buildFilterChip('전체', 'ALL'),
          SizedBox(width: 8.w),
          _buildFilterChip('매수', 'LONG'),
          SizedBox(width: 8.w),
          _buildFilterChip('매도', 'SHORT'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _filterType == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() => _filterType = value);
      },
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.signal_cellular_off,
            size: 64.w,
            color: Colors.grey,
          ),
          SizedBox(height: 16.h),
          Text(
            '신호 히스토리가 없습니다',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '서버에 연결하여 신호를 수신해보세요',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSignalList() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      itemCount: _filteredSignals.length,
      itemBuilder: (context, index) {
        final signal = _filteredSignals[index];
        return _buildSignalCard(signal);
      },
    );
  }

  Widget _buildSignalCard(TradingSignal signal) {
    final isLong = signal.signalType.toUpperCase() == 'LONG';
    final color = isLong ? Colors.green : Colors.red;

    return Card(
      margin: EdgeInsets.only(bottom: 8.h),
      child: ListTile(
        leading: Container(
          width: 40.w,
          height: 40.w,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              signal.signalIcon,
              style: TextStyle(fontSize: 20.sp),
            ),
          ),
        ),
        title: Row(
          children: [
            Text(
              signal.signalTypeKorean,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 8.w),
            Text(signal.formattedSymbol),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(signal.detailedFormattedTime),
            if (signal.source != 'WebSocket')
              Text(
                '소스: ${signal.source}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey,
                ),
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              signal.timeAgo,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey,
              ),
            ),
          ],
        ),
        onTap: () => _showSignalDetails(signal),
      ),
    );
  }

  void _showSignalDetails(TradingSignal signal) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${signal.signalTypeKorean} 신호 상세'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('심볼', signal.symbol),
            _buildDetailRow('타입', signal.signalTypeKorean),
            _buildDetailRow('시간', signal.detailedFormattedTime),
            _buildDetailRow('소스', signal.source),
            if (signal.rawLogLine != null)
              _buildDetailRow('원본 로그', signal.rawLogLine!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('닫기'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60.w,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
