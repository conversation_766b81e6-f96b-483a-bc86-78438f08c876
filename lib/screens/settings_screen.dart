import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../services/websocket_service.dart';
import '../services/notification_service.dart';
import '../services/storage_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _wakelockEnabled = true;
  bool _autoReconnect = true;
  bool _backgroundService = true;
  double _volume = 1.0;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final notificationService = context.read<NotificationService>();
    await notificationService.loadSettings();
    
    setState(() {
      _soundEnabled = notificationService.soundEnabled;
      _vibrationEnabled = notificationService.vibrationEnabled;
      _wakelockEnabled = notificationService.wakelockEnabled;
      _volume = notificationService.volume;
      _autoReconnect = StorageService.isAutoReconnectEnabled();
      _backgroundService = StorageService.isBackgroundServiceEnabled();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('설정'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // 알림 설정
            _buildNotificationSettings(),
            SizedBox(height: 16.h),
            
            // 연결 설정
            _buildConnectionSettings(),
            SizedBox(height: 16.h),
            
            // 앱 정보
            _buildAppInfo(),
            SizedBox(height: 16.h),
            
            // 데이터 관리
            _buildDataManagement(),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '알림 설정',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 12.h),
            
            // 사운드 설정
            SwitchListTile(
              title: const Text('알림 소리'),
              subtitle: const Text('신호 수신 시 소리 재생'),
              value: _soundEnabled,
              onChanged: (value) async {
                setState(() => _soundEnabled = value);
                await context.read<NotificationService>().setSoundEnabled(value);
              },
            ),
            
            // 진동 설정
            SwitchListTile(
              title: const Text('진동'),
              subtitle: const Text('신호 수신 시 진동'),
              value: _vibrationEnabled,
              onChanged: (value) async {
                setState(() => _vibrationEnabled = value);
                await context.read<NotificationService>().setVibrationEnabled(value);
              },
            ),
            
            // 화면 깨우기 설정
            SwitchListTile(
              title: const Text('화면 깨우기'),
              subtitle: const Text('신호 수신 시 화면 자동 켜기'),
              value: _wakelockEnabled,
              onChanged: (value) async {
                setState(() => _wakelockEnabled = value);
                await context.read<NotificationService>().setWakelockEnabled(value);
              },
            ),
            
            // 볼륨 설정
            ListTile(
              title: const Text('알림 볼륨'),
              subtitle: Slider(
                value: _volume,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: '${(_volume * 100).round()}%',
                onChanged: (value) async {
                  setState(() => _volume = value);
                  await context.read<NotificationService>().setVolume(value);
                },
              ),
            ),
            
            // 테스트 알림 버튼
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  context.read<NotificationService>().showTestNotification();
                },
                icon: const Icon(Icons.notifications),
                label: const Text('테스트 알림'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionSettings() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '연결 설정',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 12.h),
            
            // 자동 재연결
            SwitchListTile(
              title: const Text('자동 재연결'),
              subtitle: const Text('연결이 끊어지면 자동으로 재연결 시도'),
              value: _autoReconnect,
              onChanged: (value) async {
                setState(() => _autoReconnect = value);
                await StorageService.setAutoReconnect(value);
              },
            ),
            
            // 백그라운드 서비스
            SwitchListTile(
              title: const Text('백그라운드 서비스'),
              subtitle: const Text('앱이 백그라운드에 있을 때도 신호 수신'),
              value: _backgroundService,
              onChanged: (value) async {
                setState(() => _backgroundService = value);
                await StorageService.setBackgroundService(value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppInfo() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '앱 정보',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 12.h),
            
            ListTile(
              leading: const Icon(Icons.info),
              title: const Text('앱 이름'),
              subtitle: const Text('오픈시스템즈 사용자 앱'),
            ),
            
            ListTile(
              leading: const Icon(Icons.code),
              title: const Text('버전'),
              subtitle: const Text('1.0.0+1'),
            ),
            
            ListTile(
              leading: const Icon(Icons.business),
              title: const Text('개발사'),
              subtitle: const Text('오픈시스템즈'),
            ),
            
            ListTile(
              leading: const Icon(Icons.description),
              title: const Text('설명'),
              subtitle: const Text('실시간 트레이딩 신호 수신 앱'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataManagement() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '데이터 관리',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 12.h),
            
            // 통계 초기화
            ListTile(
              leading: const Icon(Icons.bar_chart),
              title: const Text('통계 초기화'),
              subtitle: const Text('수신 통계를 초기화합니다'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () => _showClearStatisticsDialog(),
            ),
            
            // 히스토리 삭제
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('히스토리 삭제'),
              subtitle: const Text('신호 히스토리를 삭제합니다'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () => _showClearHistoryDialog(),
            ),
            
            // 모든 데이터 삭제
            ListTile(
              leading: const Icon(Icons.delete_forever),
              title: const Text('모든 데이터 삭제'),
              subtitle: const Text('앱의 모든 데이터를 삭제합니다'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () => _showClearAllDataDialog(),
            ),
          ],
        ),
      ),
    );
  }

  void _showClearStatisticsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('통계 초기화'),
        content: const Text('수신 통계를 초기화하시겠습니까?\n이 작업은 되돌릴 수 없습니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () async {
              await context.read<WebSocketService>().clearStatistics();
              Navigator.pop(context);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('통계가 초기화되었습니다')),
                );
              }
            },
            child: const Text('초기화'),
          ),
        ],
      ),
    );
  }

  void _showClearHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('히스토리 삭제'),
        content: const Text('신호 히스토리를 삭제하시겠습니까?\n이 작업은 되돌릴 수 없습니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () async {
              await StorageService.remove('signal_history');
              Navigator.pop(context);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('히스토리가 삭제되었습니다')),
                );
              }
            },
            child: const Text('삭제'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('모든 데이터 삭제'),
        content: const Text('앱의 모든 데이터를 삭제하시겠습니까?\n설정, 통계, 히스토리가 모두 삭제됩니다.\n이 작업은 되돌릴 수 없습니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () async {
              await StorageService.clear();
              Navigator.pop(context);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('모든 데이터가 삭제되었습니다')),
                );
              }
            },
            child: const Text('삭제'),
          ),
        ],
      ),
    );
  }
}
