#!/bin/bash

# 오픈시스템즈 사용자용 모바일 앱 빌드 스크립트

echo "🚀 오픈시스템즈 사용자용 모바일 앱 빌드 시작"
echo "================================================"

# 현재 디렉토리 확인
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ 오류: pubspec.yaml 파일을 찾을 수 없습니다."
    echo "   opensystems_user_mobile 디렉토리에서 실행해주세요."
    exit 1
fi

# Flutter 설치 확인
if ! command -v flutter &> /dev/null; then
    echo "❌ 오류: Flutter가 설치되지 않았습니다."
    echo "   https://flutter.dev/docs/get-started/install 에서 Flutter를 설치해주세요."
    exit 1
fi

# Flutter 버전 확인
echo "📋 Flutter 버전 확인..."
flutter --version

# 의존성 설치
echo ""
echo "📦 의존성 설치 중..."
flutter pub get

if [ $? -ne 0 ]; then
    echo "❌ 의존성 설치 실패"
    exit 1
fi

# 코드 분석
echo ""
echo "🔍 코드 분석 중..."
flutter analyze

if [ $? -ne 0 ]; then
    echo "⚠️  경고: 코드 분석에서 문제가 발견되었습니다."
    echo "   계속 진행하시겠습니까? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "빌드가 취소되었습니다."
        exit 1
    fi
fi

# 릴리즈 APK 빌드
echo ""
echo "🔨 릴리즈 APK 빌드 중..."
flutter build apk --release

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 빌드 성공!"
    echo "================================================"
    echo "📱 APK 파일 위치:"
    echo "   build/app/outputs/flutter-apk/app-release.apk"
    echo ""
    echo "📋 설치 방법:"
    echo "   1. Android 기기에서 '알 수 없는 소스' 허용"
    echo "   2. APK 파일을 기기로 전송"
    echo "   3. 파일 매니저에서 APK 파일 실행"
    echo "   4. 설치 진행"
    echo ""
    echo "🔧 권한 설정:"
    echo "   - 알림 권한 허용"
    echo "   - 배터리 최적화 제외"
    echo "   - 백그라운드 앱 새로고침 허용"
    echo ""
    echo "🌐 서버 연결:"
    echo "   - 관리자 웹앱이 실행 중이어야 함"
    echo "   - 같은 네트워크에 연결"
    echo "   - 서버 URL: ws://[관리자PC_IP]:8080/ws"
    echo "================================================"
else
    echo ""
    echo "❌ 빌드 실패"
    echo "   오류를 확인하고 다시 시도해주세요."
    exit 1
fi
