# 📱 오픈시스템즈 사용자용 모바일 앱 설치 가이드

## 🎯 개요

이 가이드는 오픈시스템즈 트레이딩 신호 수신용 모바일 앱의 설치 및 설정 방법을 안내합니다.

## 📋 사전 요구사항

### 하드웨어 요구사항
- Android 7.0 (API 24) 이상
- RAM 2GB 이상 권장
- 저장공간 100MB 이상

### 네트워크 요구사항
- WiFi 또는 모바일 데이터 연결
- 관리자 PC와 같은 네트워크 (WiFi 사용 시)

## 🔨 앱 빌드 방법

### 1. 개발 환경 설정

#### Flutter 설치
1. [Flutter 공식 사이트](https://flutter.dev/docs/get-started/install)에서 Flutter SDK 다운로드
2. 환경 변수 PATH에 Flutter bin 디렉토리 추가
3. `flutter doctor` 명령어로 설치 확인

#### Android Studio 설치 (선택사항)
1. [Android Studio](https://developer.android.com/studio) 다운로드 및 설치
2. Android SDK 및 도구 설치
3. USB 디버깅 활성화

### 2. 앱 빌드

#### Windows에서 빌드
```cmd
cd opensystems_user_mobile
build_apk.bat
```

#### macOS/Linux에서 빌드
```bash
cd opensystems_user_mobile
chmod +x build_apk.sh
./build_apk.sh
```

#### 수동 빌드
```bash
cd opensystems_user_mobile
flutter pub get
flutter build apk --release
```

## 📱 앱 설치 방법

### 1. APK 파일 위치
빌드 완료 후 APK 파일은 다음 위치에 생성됩니다:
```
opensystems_user_mobile/build/app/outputs/flutter-apk/app-release.apk
```

### 2. Android 기기 설정

#### 알 수 없는 소스 허용
1. **설정** → **보안** (또는 **생체 인식 및 보안**)
2. **알 수 없는 소스** 또는 **알 수 없는 앱 설치** 활성화
3. 또는 설치 시 나타나는 팝업에서 허용

#### 개발자 옵션 활성화 (선택사항)
1. **설정** → **휴대전화 정보**
2. **빌드 번호**를 7번 연속 터치
3. **설정** → **개발자 옵션** → **USB 디버깅** 활성화

### 3. APK 설치

#### 방법 1: USB 케이블 사용
1. Android 기기를 PC에 USB로 연결
2. APK 파일을 기기 저장소로 복사
3. 기기에서 파일 매니저 앱 실행
4. APK 파일 선택 후 설치

#### 방법 2: 클라우드 저장소 사용
1. APK 파일을 Google Drive, Dropbox 등에 업로드
2. 기기에서 해당 앱으로 APK 다운로드
3. 다운로드 완료 후 설치

#### 방법 3: ADB 사용 (개발자용)
```bash
adb install app-release.apk
```

## ⚙️ 앱 초기 설정

### 1. 권한 허용

앱 설치 후 다음 권한들을 허용해야 합니다:

#### 필수 권한
- **알림**: 신호 수신 알림 표시
- **네트워크 접근**: 서버 연결

#### 권장 권한
- **배터리 최적화 제외**: 백그라운드 실행 보장
- **자동 시작**: 기기 재시작 시 자동 실행
- **백그라운드 앱 새로고침**: 백그라운드에서 신호 수신

### 2. 배터리 최적화 제외 설정

#### Samsung 기기
1. **설정** → **기기 케어** → **배터리**
2. **앱 전원 관리** → **최적화되지 않은 앱**
3. **오픈시스템즈 신호수신** 앱 선택 후 **최적화 안 함**

#### LG 기기
1. **설정** → **배터리** → **배터리 사용량**
2. **배터리 최적화 무시** → 앱 선택

#### 기타 기기
1. **설정** → **배터리** → **배터리 최적화**
2. **최적화되지 않음** → 앱 선택

### 3. 서버 연결 설정

#### 관리자 PC IP 주소 확인
1. 관리자 PC에서 `ipconfig` (Windows) 또는 `ifconfig` (Mac/Linux) 실행
2. IPv4 주소 확인 (예: *************)

#### 앱에서 서버 URL 설정
1. 앱 실행 후 **연결 설정** 탭 이동
2. **서버 URL** 입력: `ws://[관리자PC_IP]:8080/ws`
   - 예: `ws://*************:8080/ws`
3. **저장** 버튼 클릭
4. **연결** 버튼으로 연결 테스트

## 🔧 문제 해결

### 설치 문제

#### "앱이 설치되지 않음" 오류
- 알 수 없는 소스 허용 확인
- 저장공간 부족 확인
- 기존 앱 제거 후 재설치

#### "파일이 손상됨" 오류
- APK 파일 다시 다운로드
- 다른 방법으로 파일 전송

### 연결 문제

#### "서버에 연결할 수 없음" 오류
1. **네트워크 확인**
   - WiFi 연결 상태 확인
   - 관리자 PC와 같은 네트워크인지 확인

2. **서버 상태 확인**
   - 관리자 웹앱이 실행 중인지 확인
   - 포트 8080이 열려있는지 확인

3. **방화벽 설정**
   - Windows 방화벽에서 포트 8080 허용
   - 공유기 설정에서 포트 포워딩 확인

#### "연결이 자주 끊어짐" 문제
- 배터리 최적화 제외 설정 확인
- 백그라운드 앱 새로고침 허용
- 절전 모드 해제

### 알림 문제

#### "알림이 오지 않음" 문제
1. **알림 권한 확인**
   - 앱 설정에서 알림 허용 확인
   - 시스템 알림 설정 확인

2. **방해 금지 모드**
   - 방해 금지 모드 해제
   - 중요 알림 허용 설정

3. **앱 설정 확인**
   - 설정 탭에서 알림 설정 확인
   - 테스트 알림으로 동작 확인

## 📞 지원

### 기술 지원
- 이메일: <EMAIL>
- 전화: 02-1234-5678

### 자주 묻는 질문
1. **Q: 여러 기기에서 동시 사용 가능한가요?**
   A: 네, 여러 기기에서 동시에 신호를 수신할 수 있습니다.

2. **Q: 모바일 데이터로도 사용 가능한가요?**
   A: 네, 하지만 관리자 PC의 공인 IP와 포트 포워딩 설정이 필요합니다.

3. **Q: 배터리 소모가 많나요?**
   A: 백그라운드 최적화로 배터리 소모를 최소화했습니다.

---

**© 2025 오픈시스템즈. 모든 권리 보유.**
